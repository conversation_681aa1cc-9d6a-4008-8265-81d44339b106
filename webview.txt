10:37:01.615 18804 18804 I player_log_tag: .onProgress->progress=328003;duration=477962
10:37:01.615 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=10080
10:37:01.615 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=328003
10:37:01.615 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 960, 已经累加的时间： 10080
10:37:01.643 18804 18960 I broadcastDataList:: 2
10:37:01.644 18804 18848 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:37:01.644 18804 18848 D CommonParamModule: ensureValidAccessToken: 当前 openId=wa33982025061710012998, access_token=null, userId=null
10:37:01.644 18804 18848 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:37:01.644 18804 18848 D CommonParamModule: provideKaolaParams: 当前的 openId: wa33982025061710012998
10:37:01.644 18804 18848 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:37:01.644 18804 18848 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:37:01.644 18804 18848 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {coordtype=, capabilities=PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=121.634334, openid=wa33982025061710012998, packagename=com.edog.car.mengshi_kradio, sign=caf4f109ac5b35e02982afdcde259b48, channel=mengshi_kradio, deviceid=devi1a9718b377b801ad, version=3.01.2.0013, sdkversion=3.0.11.pre17, carType=acadia, appid=wa3398, udid=devi1a9718b377b801ad, lat=31.290464}
10:37:01.645 18804 18848 I RequestInterceptManager: 替换完成 url= https://iovopen.radio.cn/v3/broadcast/programlist?bid=1600000000616&coordtype=&capabilities=PAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.634334&openid=wa33982025061710012998&packagename=com.edog.car.mengshi_kradio&sign=caf4f109ac5b35e02982afdcde259b48&channel=mengshi_kradio&deviceid=devi1a9718b377b801ad&version=3.01.2.0013&sdkversion=3.0.11.pre17&carType=acadia&appid=wa3398&udid=devi1a9718b377b801ad&lat=31.290464 timestamp:1756089421645
10:37:01.645 18804 18848 I K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
10:37:01.645 18804 18848 I K-radio-Request: │ │ URL: https://iovopen.radio.cn/v3/broadcast/programlist?bid=1600000000616&coordtype=&capabilities=PAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.634334&openid=wa33982025061710012998&packagename=com.edog.car.mengshi_kradio&sign=caf4f109ac5b35e02982afdcde259b48&channel=mengshi_kradio&deviceid=devi1a9718b377b801ad&version=3.01.2.0013&sdkversion=3.0.11.pre17&carType=acadia&appid=wa3398&udid=devi1a9718b377b801ad&lat=31.290464
10:37:01.645 18804 18848 I K-radio-Request: │ │ 
10:37:01.645 18804 18848 I K-radio-Request: │ │ Method: @GET
10:37:01.645 18804 18848 I K-radio-Request: │ │ 
10:37:01.645 18804 18848 I K-radio-Request: │ │ Headers:
10:37:01.645 18804 18848 I K-radio-Request: │ │ ┌ Host: iovopen.radio.cn
10:37:01.645 18804 18848 I K-radio-Request: │ │ ├ Connection: Keep-Alive
10:37:01.645 18804 18848 I K-radio-Request: │ │ ├ Accept-Encoding: gzip
10:37:01.645 18804 18848 I K-radio-Request: │ │ └ User-Agent: okhttp/3.14.9
10:37:01.645 18804 18848 I K-radio-Request: │ │ Omitted request body
10:37:01.645 18804 18848 I K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:02.645 18804 18960 I broadcastDataList:: 1
10:37:02.646 18804 18980 W CommonParamModule: ensureValidAccessToken: AccessToken 数据不完整，尝试重新获取
10:37:02.646 18804 18980 D CommonParamModule: ensureValidAccessToken: 当前 openId=wa33982025061710012998, access_token=null, userId=null
10:37:02.646 18804 18980 W CommonParamModule: ensureValidAccessToken: 第一次重试后数据仍不完整
10:37:02.646 18804 18980 D CommonParamModule: provideKaolaParams: 当前的 openId: wa33982025061710012998
10:37:02.647 18804 18980 W CommonParamModule: provideKaolaParams: userId 为空，替换为 deviceId
10:37:02.647 18804 18980 W CommonParamModule: provideKaolaParams: access_token 为空，不会被添加到参数中
10:37:02.647 18804 18980 D CommonParamModule: provideKaolaParams: 最终生成的公共参数: {coordtype=, capabilities=PAY_CONTENT_SUPPORTTED,MEDIA_URL_MUST_BE_HTTPS, os=android, lng=121.634334, openid=wa33982025061710012998, packagename=com.edog.car.mengshi_kradio, sign=caf4f109ac5b35e02982afdcde259b48, channel=mengshi_kradio, deviceid=devi1a9718b377b801ad, version=3.01.2.0013, sdkversion=3.0.11.pre17, carType=acadia, appid=wa3398, udid=devi1a9718b377b801ad, lat=31.290464}
10:37:02.648 18804 18980 I RequestInterceptManager: 替换完成 url= https://iovopen.radio.cn/v3/broadcast/programlist?bid=1600000004719&coordtype=&capabilities=PAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.634334&openid=wa33982025061710012998&packagename=com.edog.car.mengshi_kradio&sign=caf4f109ac5b35e02982afdcde259b48&channel=mengshi_kradio&deviceid=devi1a9718b377b801ad&version=3.01.2.0013&sdkversion=3.0.11.pre17&carType=acadia&appid=wa3398&udid=devi1a9718b377b801ad&lat=31.290464 timestamp:1756089422648
10:37:02.648 18804 18980 I K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
10:37:02.649 18804 18980 I K-radio-Request: │ │ URL: https://iovopen.radio.cn/v3/broadcast/programlist?bid=1600000004719&coordtype=&capabilities=PAY_CONTENT_SUPPORTTED%2CMEDIA_URL_MUST_BE_HTTPS&os=android&lng=121.634334&openid=wa33982025061710012998&packagename=com.edog.car.mengshi_kradio&sign=caf4f109ac5b35e02982afdcde259b48&channel=mengshi_kradio&deviceid=devi1a9718b377b801ad&version=3.01.2.0013&sdkversion=3.0.11.pre17&carType=acadia&appid=wa3398&udid=devi1a9718b377b801ad&lat=31.290464
10:37:02.649 18804 18980 I K-radio-Request: │ │ 
10:37:02.649 18804 18980 I K-radio-Request: │ │ Method: @GET
10:37:02.649 18804 18980 I K-radio-Request: │ │ 
10:37:02.649 18804 18980 I K-radio-Request: │ │ Headers:
10:37:02.649 18804 18980 I K-radio-Request: │ │ ┌ Host: iovopen.radio.cn
10:37:02.649 18804 18980 I K-radio-Request: │ │ ├ Connection: Keep-Alive
10:37:02.649 18804 18980 I K-radio-Request: │ │ ├ Accept-Encoding: gzip
10:37:02.649 18804 18980 I K-radio-Request: │ │ └ User-Agent: okhttp/3.14.9
10:37:02.649 18804 18980 I K-radio-Request: │ │ Omitted request body
10:37:02.649 18804 18980 I K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:02.695 18804 18804 I player_log_tag: .onProgress->progress=329083;duration=477962
10:37:02.695 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=11160
10:37:02.695 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=329083
10:37:02.696 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1080, 已经累加的时间： 11160
10:37:02.696 18804 18886 I HistoryItem: timeStamp = 1756089422696
10:37:02.696 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:02.696 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:37:02.703 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 6.000 ms
10:37:02.704 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@6cc5c32不需要切换线程，直接运行。
10:37:02.704 18804 18804 D K-radio : │ proceed: result=0
10:37:02.704 18804 18804 D K-radio : │ proceed: result=0
10:37:02.705 18804 18843 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:02.705 18804 18843 D greenDAO: Values for query: [99]
10:37:02.706 18804 18843 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:02.706 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 0.000 ms
10:37:02.706 18804 18843 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:02.709 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:02.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:02.710 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:02.710 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:02.710 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:02.710 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:02.711 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:02.711 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:02.711 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:02.711 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:02.711 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:03.154 18804 18997 V IJKMEDIA: read thread: run in read loop - 2876857
10:37:03.647 18804 18960 I broadcastDataList:: 0
10:37:03.656 18804 18804 I player_log_tag: .onProgress->progress=330042;duration=477962
10:37:03.656 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=12119
10:37:03.656 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=330042
10:37:03.656 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 959, 已经累加的时间： 12119
10:37:04.616 18804 18804 I player_log_tag: .onProgress->progress=331003;duration=477962
10:37:04.616 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=13080
10:37:04.617 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=331003
10:37:04.617 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 961, 已经累加的时间： 13080
10:37:04.648 18804 18960 I broadcastDataList:: 0
10:37:04.704 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851897
10:37:05.649 18804 18960 I broadcastDataList:: 0
10:37:05.695 18804 18804 I player_log_tag: .onProgress->progress=332083;duration=477962
10:37:05.695 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=14160
10:37:05.696 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=332083
10:37:05.696 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1080, 已经累加的时间： 14160
10:37:05.696 18804 18872 I HistoryItem: timeStamp = 1756089425696
10:37:05.696 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:05.697 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:37:05.704 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 7.000 ms
10:37:05.705 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@fb8d7c7不需要切换线程，直接运行。
10:37:05.705 18804 18804 D K-radio : │ proceed: result=0
10:37:05.706 18804 18804 D K-radio : │ proceed: result=0
10:37:05.706 18804 18982 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:05.706 18804 18982 D greenDAO: Values for query: [99]
10:37:05.708 18804 18982 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:05.708 18804 18982 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 0.000 ms
10:37:05.708 18804 18982 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:05.711 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:05.711 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:05.711 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:05.711 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:05.711 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:05.711 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:05.712 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:05.712 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:05.712 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:05.712 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:05.712 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:05.712 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:05.712 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:05.712 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:05.712 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:05.712 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:05.712 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:05.712 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:05.712 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:06.650 18804 18960 I broadcastDataList:: 0
10:37:06.655 18804 18804 I player_log_tag: .onProgress->progress=333043;duration=477962
10:37:06.655 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=15120
10:37:06.655 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=333043
10:37:06.655 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 960, 已经累加的时间： 15120
10:37:07.615 18804 18804 I player_log_tag: .onProgress->progress=334003;duration=477962
10:37:07.616 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=16080
10:37:07.616 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=334003
10:37:07.616 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 960, 已经累加的时间： 16080
10:37:07.651 18804 18960 I broadcastDataList:: 0
10:37:07.705 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851894
10:37:08.500 18804 18832 I NetworkManager: Socket test thread is running!
10:37:08.500 18804 18832 I NetworkManager: Socket test start
10:37:08.501 18804 18832 I NetworkManager: ip: iovopen.radio.cn
10:37:08.536 18804 18832 I NetworkManager: Socket test success finish
10:37:08.653 18804 18960 I broadcastDataList:: 0
10:37:08.695 18804 18804 I player_log_tag: .onProgress->progress=335082;duration=477962
10:37:08.695 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=17159
10:37:08.695 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=335082
10:37:08.696 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1079, 已经累加的时间： 17159
10:37:08.696 18804 18981 I HistoryItem: timeStamp = 1756089428696
10:37:08.696 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:08.696 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:37:08.703 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 6.000 ms
10:37:08.704 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@2419e19不需要切换线程，直接运行。
10:37:08.704 18804 18804 D K-radio : │ proceed: result=0
10:37:08.704 18804 18804 D K-radio : │ proceed: result=0
10:37:08.704 18804 18860 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:08.704 18804 18860 D greenDAO: Values for query: [99]
10:37:08.705 18804 18860 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:08.705 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 0.000 ms
10:37:08.705 18804 18860 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:08.707 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:08.707 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:08.707 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:08.707 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:08.707 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:08.707 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:08.707 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:08.707 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:08.707 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:08.707 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:08.707 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:09.206 18804 18997 V IJKMEDIA: read thread: run in read loop - 2882909
10:37:09.655 18804 18960 I broadcastDataList:: 0
10:37:09.655 18804 18804 I player_log_tag: .onProgress->progress=336043;duration=477962
10:37:09.656 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=18120
10:37:09.656 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=336043
10:37:09.656 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 961, 已经累加的时间： 18120
10:37:10.615 18804 18804 I player_log_tag: .onProgress->progress=337002;duration=477962
10:37:10.615 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=19079
10:37:10.615 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=337002
10:37:10.615 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 959, 已经累加的时间： 19079
10:37:10.655 18804 18960 I broadcastDataList:: 0
10:37:10.705 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851891
10:37:11.654 18804 18960 I broadcastDataList:: 0
10:37:11.695 18804 18804 I player_log_tag: .onProgress->progress=338084;duration=477962
10:37:11.695 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=20161
10:37:11.695 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=338084
10:37:11.696 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1082, 已经累加的时间： 20161
10:37:11.696 18804 18983 I HistoryItem: timeStamp = 1756089431696
10:37:11.697 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 1.000 ms
10:37:11.697 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:37:11.704 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 7.000 ms
10:37:11.705 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@c7d4aea不需要切换线程，直接运行。
10:37:11.705 18804 18804 D K-radio : │ proceed: result=0
10:37:11.705 18804 18804 D K-radio : │ proceed: result=0
10:37:11.706 18804 18848 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:11.706 18804 18848 D greenDAO: Values for query: [99]
10:37:11.707 18804 18848 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:11.707 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 0.000 ms
10:37:11.707 18804 18848 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:11.709 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:11.710 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:11.710 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:11.710 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:11.710 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:11.711 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:11.711 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:11.711 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:11.711 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:11.711 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:11.711 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:12.226 18804 18804 D DecorView[SettingActivity]: ====>>app dispatchTouchEvent: action:ACTION_DOWN
10:37:12.324 18804 18804 D DecorView[SettingActivity]: ====>>app dispatchTouchEvent: action:ACTION_UP
10:37:12.329 18804 18804 I APK_NetworkUtil: isNetworkAvailableDefault infos = [type: WIFI[], state: CONNECTED/CONNECTED, reason: (unspecified), extra: , failover: false, available: true, roaming: false]
10:37:12.329 18804 18804 I APK_NetworkUtil: isNetworkAvailableDefault isConnected = true, netType = 1
10:37:12.329 18804 18804 I NetworkManager: getNetworkState:1
10:37:12.329 18804 18804 I APK_NetworkUtil: isNetworkAvailableDefault networkState = 1
10:37:12.345 18804 18804 I ActivityLifecycle: -------------------SettingActivity onPause Start (onActivityPrePaused) -------------------
10:37:12.345 18804 18804 I ActivityLifecycle: -------------------SettingActivity onPause Finished (onActivityPaused) -------------------
10:37:12.345 18804 18804 D ActivityLifecycle: activity set size 0
10:37:12.346 18804 18804 V FragmentManager: computeExpectedState() of 5 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.346 18804 18804 D FragmentManager: movefrom RESUMED: t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.347 18804 18804 D FragmentLifecycle: -------------------onFragmentPaused: t---------------------------
10:37:12.347 18804 18804 V FragmentManager: computeExpectedState() of 5 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.347 18804 18804 V FragmentManager: computeExpectedState() of 5 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.347 18804 18804 V FragmentManager: computeExpectedState() of 5 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.347 18804 18804 D FragmentManager: movefrom RESUMED: j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.348 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.348 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:37:12.354 18804 18804 I report_tag: │ 添加数据上报事件.是否立即:false事件id = 210004 json = {"pageid":"","pagetime":"13885","action_id":"4234","app_mode":"0","app_version":"3.01","app_version2":"300120013","appid":"wa3398","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"0","channel":"com.edog.car.mengshi_kradio","developer":"5680411","dsource":"vehicleeqv2","eventcode":"210004","ip":"*************","lat":"31.290464","lon":"121.634334","manufacturer":"DFTC","market_type":"","model":"M18","network":"1","oem":"","openid":"wa33982025061710012998","operator":"0","os":"android","osversion":"11","page":"122114","playid":"841fbd26a899853a6f8f4a597e24860b","product_id":"1","report_timely":"0","screen_direction":"0","screen_height":"962","screen_width":"1920","sessionid":"16","sourcetype":"3","timestamp":"1756089432347","udid":"devi1a9718b377b801ad","wifi":"1"}
10:37:12.354 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.355 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:37:12.359 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "COMMIT;" took 10.000 ms
10:37:12.360 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.360 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:37:12.361 18804 18804 I report_tag: │ 添加数据上报事件.是否立即:false事件id = 330004 json = {"dialogid":"1","duration":13885,"pageId":"122114","action_id":"4235","app_mode":"0","app_version":"3.01","app_version2":"300120013","appid":"wa3398","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"0","channel":"com.edog.car.mengshi_kradio","developer":"5680411","dsource":"vehicleeqv2","eventcode":"330004","ip":"*************","lat":"31.290464","lon":"121.634334","manufacturer":"DFTC","market_type":"","model":"M18","network":"1","oem":"","openid":"wa33982025061710012998","operator":"0","os":"android","osversion":"11","page":"122114","playid":"841fbd26a899853a6f8f4a597e24860b","product_id":"1","report_timely":"0","screen_direction":"0","screen_height":"962","screen_width":"1920","sessionid":"16","sourcetype":"3","timestamp":"1756089432355","udid":"devi1a9718b377b801ad","wifi":"1"}
10:37:12.361 18804 18804 I BaseDialogFragment: report=13885
10:37:12.361 18804 18804 D FragmentLifecycle: -------------------onFragmentPaused: j---------------------------
10:37:12.361 18804 18804 V FragmentManager: computeExpectedState() of 5 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.361 18804 18804 I chatty  : uid=10084 com.edog.car:25 identical 1 line
10:37:12.361 18804 18804 V FragmentManager: computeExpectedState() of 5 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.361 18804 18804 V FragmentManager: computeExpectedState() of 5 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.371 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 16.000 ms
10:37:12.371 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:12.378 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "COMMIT;" took 18.000 ms
10:37:12.379 18804 18886 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 1.000 ms
10:37:12.379 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:37:12.388 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onCreate Start (onActivityPreCreated) -------------------
10:37:12.391 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onCreate Finished (onActivityCreated) -------------------
10:37:12.392 18804 18804 D Activity: ====>> oncreate GCB_MODEL:---0
10:37:12.410 18804 18804 D ConnectivityManager: StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:3669)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:4191)] [RK.e(chromium-SystemWebView.apk-default-410412053:8)] [Po.b(chromium-SystemWebView.apk-default-410412053:1)] [org.chromium.android_webview.AwContentsLifecycleNotifier.onFirstWebViewCreated(chromium-SystemWebView.apk-default-410412053:3)] [J.N.MFiR_zHY(Native Method)] [org.chromium.android_webview.AwContents.<init>(chromium-SystemWebView.apk-default-410412053:70)] [com.android.webview.chromium.WebViewChromium.a(chromium-SystemWebView.apk-default-410412053:7)] [B9.run(chromium-SystemWebView.apk-default-410412053:1)] [Dq.b(chromium-SystemWebView.apk-default-410412053:3)] [Bq.run(chromium-SystemWebView.apk-default-410412053:1)] [org.chromium.base.task.PostTask.b(chromium-SystemWebView.apk-default-410412053:3)] [Dq.a(chromium-SystemWebView.apk-default-410412053:3)] [com.android.webview.chromium.WebViewChromium.init(chromium-SystemWebView.apk-default-410412053:74)] [android.webkit.WebView.<init>(WebView.java:435)] [android.webkit.WebView.<init>(WebView.java:355)] [android.webkit.WebView.<init>(WebView.java:337)] [android.webkit.WebView.<init>(WebView.java:324)] [java.lang.reflect.Constructor.newInstance0(Native Method)] [java.lang.reflect.Constructor.newInstance(Constructor.java:343)] [skin.support.app.e.b(SourceFile:7)] [skin.support.app.e.f(SourceFile:7)] [skin.support.app.e.c(SourceFile:3)] [skin.support.app.d.c(SourceFile:6)] [skin.support.app.d.onCreateView(SourceFile:1)] [android.view.LayoutInflater.tryCreateView(LayoutInflater.java:1059)] [android.view.LayoutInflater.createViewFromTag(LayoutInflater.java:995)] [android.view.LayoutInflater.createViewFromTag(LayoutInflater.java:959)] [android.view.LayoutInflater.rInflate(LayoutInflater.java:1121)] [android.view.LayoutInflater.rInflateChildren(LayoutInflater.java:1082)] [android.view.LayoutInflater.infla ...
10:37:12.415 18804 18804 D ConnectivityManager: StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:3669)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:4079)] [RK.e(chromium-SystemWebView.apk-default-410412053:27)] [Po.b(chromium-SystemWebView.apk-default-410412053:1)] [org.chromium.android_webview.AwContentsLifecycleNotifier.onFirstWebViewCreated(chromium-SystemWebView.apk-default-410412053:3)] [J.N.MFiR_zHY(Native Method)] [org.chromium.android_webview.AwContents.<init>(chromium-SystemWebView.apk-default-410412053:70)] [com.android.webview.chromium.WebViewChromium.a(chromium-SystemWebView.apk-default-410412053:7)] [B9.run(chromium-SystemWebView.apk-default-410412053:1)] [Dq.b(chromium-SystemWebView.apk-default-410412053:3)] [Bq.run(chromium-SystemWebView.apk-default-410412053:1)] [org.chromium.base.task.PostTask.b(chromium-SystemWebView.apk-default-410412053:3)] [Dq.a(chromium-SystemWebView.apk-default-410412053:3)] [com.android.webview.chromium.WebViewChromium.init(chromium-SystemWebView.apk-default-410412053:74)] [android.webkit.WebView.<init>(WebView.java:435)] [android.webkit.WebView.<init>(WebView.java:355)] [android.webkit.WebView.<init>(WebView.java:337)] [android.webkit.WebView.<init>(WebView.java:324)] [java.lang.reflect.Constructor.newInstance0(Native Method)] [java.lang.reflect.Constructor.newInstance(Constructor.java:343)] [skin.support.app.e.b(SourceFile:7)] [skin.support.app.e.f(SourceFile:7)] [skin.support.app.e.c(SourceFile:3)] [skin.support.app.d.c(SourceFile:6)] [skin.support.app.d.onCreateView(SourceFile:1)] [android.view.LayoutInflater.tryCreateView(LayoutInflater.java:1059)] [android.view.LayoutInflater.createViewFromTag(LayoutInflater.java:995)] [android.view.LayoutInflater.createViewFromTag(LayoutInflater.java:959)] [android.view.LayoutInflater.rInflate(LayoutInflater.java:1121)] [android.view.LayoutInflater.rInflateChildren(LayoutInflater.java:1082)] [android.view.LayoutInflater.inflate(Lay ...
10:37:12.430 18804 18804 I WebViewActivity: 从Intent获取参数 - url:https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0, title:用户服务协议, pageId:131310
10:37:12.430 18804 18804 I WebViewActivity: url:https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0
10:37:12.431 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.433 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.445 18804 19127 I Adreno-GSL_RPC: <gsl_context_create:2927>: ctxt_id 92, sync_type 2
10:37:12.448 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPostCreate Finished (onActivityPostCreated) -------------------
10:37:12.448 18804 19058 D BluetoothAdapter: 208538626: getState(). Returning ON
10:37:12.450 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStart Start (onActivityPreStarted) -------------------
10:37:12.452 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStart Finished (onActivityStarted) -------------------
10:37:12.452 18804 19058 D BluetoothAdapter: 208538626: getState(). Returning ON
10:37:12.453 18804 18804 D ActivityLifecycle: activity set size 1
10:37:12.455 18804 18804 D BaseActivity: Theme observer registered in WebViewActivity
10:37:12.455 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.455 18804 18804 D BaseActivity: Initial theme from settings: theme.nonight
10:37:12.455 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.455 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: forceSave = true, theme = theme.nonight, oldUI skin = day.skin, preTheme = unknown
10:37:12.455 18804 18804 I KRadioConfigChangeImpl: isSameTheme: already is day theme and day skin
10:37:12.455 18804 18804 D kradio.home: 收到主题变化事件: isSameTheme
10:37:12.455 18804 18804 D kradio.home: 收到isSameTheme事件，延迟更新用户头像
10:37:12.455 18804 18804 D HistoryFragment: 收到主题变化事件: isSameTheme
10:37:12.455 18804 18804 D ComprehensiveUserInfoFragment: 收到主题变化事件: isSameTheme
10:37:12.455 18804 18804 D ComprehensiveUserInfoFragment: 收到isSameTheme事件，延迟刷新UI状态
10:37:12.456 18804 18804 W Fragmentation: Warning: Perform this commit() action after onSaveInstanceState!
10:37:12.456 18804 18804 V FragmentManager: Commit: BackStackEntry{5b4e64e com.kaolafm.kradio.user.comprehensive.ui.x}
10:37:12.456 18804 18804 D FragmentManager:   mName=com.kaolafm.kradio.user.comprehensive.ui.x mIndex=-1 mCommitted=false
10:37:12.456 18804 18804 D FragmentManager:   mTransition=#1001  Operations:
10:37:12.456 18804 18804 D FragmentManager:     Op #0: REPLACE x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:12.456 18804 18804 I SettingActivity: onThemeEvent() --- theme = isSameTheme
10:37:12.456 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.459 18804 18804 D WebViewActivity: 收到主题变化事件: isSameTheme
10:37:12.459 18804 18804 D WebViewActivity: 收到isSameTheme事件，读取当前Settings主题状态
10:37:12.459 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: isSameTheme: preTheme = unknown, newTheme = theme.nonight
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: isSameTheme: theme changed from unknown to theme.nonight
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: isSameTheme: last skin = day.skin
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: isSameTheme: already is day theme and day skin
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: old skin = day.skin
10:37:12.460 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: SAVE_THEME = theme.nonight
10:37:12.460 18804 18804 D BaseActivity: -------------------WebViewActivity onPostCreate start -------------------
10:37:12.461 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onResume Start (onActivityPreResumed) -------------------
10:37:12.461 18804 18804 D ActivityLifecycle: activity set size 1
10:37:12.461 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onResume Finished (onActivityResumed) -------------------
10:37:12.461 18804 18804 D BaseActivity: -------------------WebViewActivity onPostResume start -------------------
10:37:12.461 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPostResume Finished (onActivityPostResumed) -------------------
10:37:12.475 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.478 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 2.000 ms
10:37:12.512 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 34.000 ms
10:37:12.513 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:12.576 18804 18804 V FragmentManager: Run: BackStackEntry{5b4e64e #4 com.kaolafm.kradio.user.comprehensive.ui.x}
10:37:12.576 18804 18804 V FragmentManager: Bump nesting in BackStackEntry{5b4e64e #4 com.kaolafm.kradio.user.comprehensive.ui.x} by 1
10:37:12.576 18804 18804 V FragmentManager: computeExpectedState() of 4 for x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:12.576 18804 18804 V FragmentManager: computeExpectedState() of 4 for x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:12.580 18804 19127 W VideoCapabilities: Unrecognized level 1879048706 for video/x-vnd.on2.vp8
10:37:12.580 18804 19127 W VideoCapabilities: Unrecognized profile 1879048704 for video/x-vnd.on2.vp8
10:37:12.581 18804 19127 W VideoCapabilities: Unrecognized level 1879048706 for video/x-vnd.on2.vp8
10:37:12.581 18804 19127 W VideoCapabilities: Unrecognized profile 1879048704 for video/x-vnd.on2.vp8
10:37:12.582 18804 18982 D greenDAO: Built SQL for query: SELECT T."_id",T."TYPE",T."SEND_STR" FROM "REPORT_DATA" T  LIMIT ?
10:37:12.582 18804 18982 D greenDAO: Values for query: [10]
10:37:12.583 18804 18982 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:12.584 18804 18982 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT T."_id",T."TYPE",T."SEND_STR" FROM "REPORT_DATA" T  LIMIT ?" took 1.000 ms
10:37:12.584 18804 18982 D SQLiteCursor: received count(*) from native_fill_window: 10
10:37:12.585 18804 19127 W VideoCapabilities: Unrecognized profile/level 0/3 for video/mpeg2
10:37:12.586 18804 19127 I chatty  : uid=10084(com.edog.car) Chrome_InProcGp identical 2 lines
10:37:12.586 18804 19127 W VideoCapabilities: Unrecognized profile/level 0/3 for video/mpeg2
10:37:12.590 18804 19127 W VideoCapabilities: Unsupported mime image/vnd.android.heic
10:37:12.590 18804 19127 W VideoCapabilities: Unsupported mime image/vnd.android.heic
10:37:12.598 18804 19127 W VideoCapabilities: Unsupported mime video/raw
10:37:12.611 18804 19127 I Adreno-GSL_RPC: <gsl_context_create:2927>: ctxt_id 93, sync_type 2
10:37:12.621 18804 18804 D BaseActivity: -------------------WebViewActivity onEnterAnimationComplete start -------------------
10:37:12.621 18804 18804 D BaseActivity: -------------------WebViewActivity onEnterAnimationComplete start -------------------
10:37:12.624 18804 18804 I ReportNetworkHelper: │ request,json=["{\"buttonid\":\"103\",\"buttonname\":\"\",\"controltype\":\"1\",\"mode\":\"exposure\",\"pageId\":\"122114\",\"action_id\":\"4226\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089416967\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}","{\"buttonid\":\"49\",\"buttonname\":\"标准音质\",\"controltype\":\"1\",\"mode\":\"exposure\",\"pageId\":\"122114\",\"action_id\":\"4227\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\ ...
10:37:12.625 18804 18804 I ReportNetworkHelper: │ udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}","{\"direction\":\"up\",\"endposition\":\"608.0,679.0\",\"pageid\":\"122114\",\"resolution\":\"1920*1080\",\"startposition\":\"545.0,772.0\",\"action_id\":\"4230\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330002\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089418127\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}","{\"buttonid\":\"52\",\"buttonname\":\"用户授权条款\",\"controltype\":\"1\",\"mode\":\"click\",\"pageId\":\"122114\",\"action_id\":\"4231\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\" ...
10:37:12.625 18804 18804 I ReportNetworkHelper: │ ",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089418437\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}","{\"pageid\":\"\",\"pagetime\":\"13885\",\"action_id\":\"4234\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"210004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089432347\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}","{\"dialogid\":\"1\",\"duration\":13885,\"pageId\":\"122114\",\"action_id\":\"4235\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\" ...
10:37:12.626 18804 18981 I RequestInterceptManager: 替换完成 url= https://iovmsg.radio.cn/k timestamp:1756089432626
10:37:12.635 18804 18804 D BaseActivity: -------------------WebViewActivity onWindowFocusChanged start: hasFocus=true-------------------
10:37:12.635 18804 18981 I K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
10:37:12.635 18804 18981 I K-radio-Request: │ │ URL: https://iovmsg.radio.cn/k
10:37:12.635 18804 18981 I K-radio-Request: │ │ 
10:37:12.635 18804 18981 I K-radio-Request: │ │ Method: @POST
10:37:12.635 18804 18981 I K-radio-Request: │ │ 
10:37:12.635 18804 18981 I K-radio-Request: │ │ Headers:
10:37:12.635 18804 18981 I K-radio-Request: │ │ ┌ Content-Type: application/json; charset=utf-8
10:37:12.635 18804 18981 I K-radio-Request: │ │ ├ Content-Length: 10140
10:37:12.635 18804 18981 I K-radio-Request: │ │ ├ Host: iovmsg.radio.cn
10:37:12.635 18804 18981 I K-radio-Request: │ │ ├ Connection: Keep-Alive
10:37:12.635 18804 18981 I K-radio-Request: │ │ ├ Accept-Encoding: gzip
10:37:12.635 18804 18981 I K-radio-Request: │ │ └ User-Agent: okhttp/3.14.9
10:37:12.635 18804 18981 I K-radio-Request: │ │ Body:
10:37:12.635 18804 18981 I K-radio-Request: │ │ [
10:37:12.635 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"103\",\"buttonname\":\"\",\"controltype\":\"1\",\"mode\":\"exposure\",\"pageId\":\"122114
10:37:12.635 18804 18981 I K-radio-Request: │ │ \",\"action_id\":\"4226\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\"
10:37:12.635 18804 18981 I K-radio-Request: │ │ :\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\
10:37:12.635 18804 18981 I K-radio-Request: │ │ ":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330003
10:37:12.635 18804 18981 I K-radio-Request: │ │ \",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_ty
10:37:12.635 18804 18981 I K-radio-Request: │ │ pe\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":
10:37:12.635 18804 18981 I K-radio-Request: │ │ \"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860
10:37:12.635 18804 18981 I K-radio-Request: │ │ b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen
10:37:12.635 18804 18981 I K-radio-Request: │ │ _width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089416967\",\"udid\":\"devi1a9
10:37:12.635 18804 18981 I K-radio-Request: │ │ 718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.635 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"49\",\"buttonname\":\"标准音质\",\"controltype\":\"1\",\"mode\":\"exposure\",\"pageId\":\"122
10:37:12.635 18804 18981 I K-radio-Request: │ │ 114\",\"action_id\":\"4227\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appi
10:37:12.636 18804 18981 I K-radio-Request: │ │ d\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"chann
10:37:12.636 18804 18981 I K-radio-Request: │ │ el\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330
10:37:12.636 18804 18981 I K-radio-Request: │ │ 003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market
10:37:12.636 18804 18981 I K-radio-Request: │ │ _type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator
10:37:12.636 18804 18981 I K-radio-Request: │ │ \":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24
10:37:12.636 18804 18981 I K-radio-Request: │ │ 860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"scr
10:37:12.636 18804 18981 I K-radio-Request: │ │ een_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089416993\",\"udid\":\"devi
10:37:12.636 18804 18981 I K-radio-Request: │ │ 1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.636 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"50\",\"buttonname\":\"高品音质\",\"controltype\":\"1\",\"mode\":\"exposure\",\"pageId\":\"122
10:37:12.637 18804 18981 I K-radio-Request: │ │ 114\",\"action_id\":\"4228\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appi
10:37:12.637 18804 18981 I K-radio-Request: │ │ d\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"chann
10:37:12.637 18804 18981 I K-radio-Request: │ │ el\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330
10:37:12.637 18804 18981 I K-radio-Request: │ │ 003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market
10:37:12.637 18804 18981 I K-radio-Request: │ │ _type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator
10:37:12.637 18804 18981 I K-radio-Request: │ │ \":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24
10:37:12.637 18804 18981 I K-radio-Request: │ │ 860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"scr
10:37:12.637 18804 18981 I K-radio-Request: │ │ een_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089417002\",\"udid\":\"devi
10:37:12.637 18804 18981 I K-radio-Request: │ │ 1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.637 18804 18981 I K-radio-Request: │ │     "{\"pageid\":\"160010\",\"pagetime\":\"2729\",\"action_id\":\"4229\",\"app_mode\":\"0\",\"app_version\":\"
10:37:12.637 18804 18981 I K-radio-Request: │ │ 3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicatio
10:37:12.637 18804 18981 I K-radio-Request: │ ncode\":\"17000\",
10:37:12.637 18804 18981 I K-radio-Request: │ │ \"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"d
10:37:12.637 18804 18981 I K-radio-Request: │ │ source\":\"vehicleeqv2\",\"eventcode\":\"210004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121
10:37:12.637 18804 18981 I K-radio-Request: │ │ .634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"op
10:37:12.637 18804 18981 I K-radio-Request: │ │ enid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"160010
10:37:12.637 18804 18981 I K-radio-Request: │ │ \",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direc
10:37:12.637 18804 18981 I K-radio-Request: │ │ tion\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"t
10:37:12.637 18804 18981 I K-radio-Request: │ │ imestamp\":\"1756089417502\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.637 18804 18981 I K-radio-Request: │ │     "{\"direction\":\"up\",\"endposition\":\"608.0,679.0\",\"pageid\":\"122114\",\"resolution\":\"1920*1080\",
10:37:12.637 18804 18981 I K-radio-Request: │ │ \"startposition\":\"545.0,772.0\",\"action_id\":\"4230\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_ver
10:37:12.638 18804 18981 I K-radio-Request: │ │ sion2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"
10:37:12.638 18804 18981 I K-radio-Request: │ │ \",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehic
10:37:12.638 18804 18981 I K-radio-Request: │ │ leeqv2\",\"eventcode\":\"330002\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manu
10:37:12.638 18804 18981 I K-radio-Request: │ │ facturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982
10:37:12.639 18804 18981 I K-radio-Request: │ │ 025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"
10:37:12.639 18804 18981 I K-radio-Request: │ │ 841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"s
10:37:12.640 18804 18981 I K-radio-Request: │ │ creen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"175
10:37:12.640 18804 18981 I K-radio-Request: │ │ 6089418127\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.640 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"52\",\"buttonname\":\"用户授权条款\",\"controltype\":\"1\",\"mode\":\"click\",\"pageId\":\"1221
10:37:12.640 18804 18981 I K-radio-Request: │ │ 14\",\"action_id\":\"4231\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid
10:37:12.640 18804 18981 I K-radio-Request: │ │ \":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channe
10:37:12.640 18804 18981 I K-radio-Request: │ │ l\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"3300
10:37:12.640 18804 18981 I K-radio-Request: │ │ 03\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_
10:37:12.640 18804 18981 I K-radio-Request: │ │ type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\
10:37:12.640 18804 18981 I K-radio-Request: │ │ ":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e248
10:37:12.640 18804 18981 I K-radio-Request: │ │ 60b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"scre
10:37:12.640 18804 18981 I K-radio-Request: │ │ en_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089418398\",\"udid\":\"devi1
10:37:12.640 18804 18981 I K-radio-Request: │ │ a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.640 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"14\",\"buttonname\":\"继续使用\",\"controltype\":\"1\",\"dialogid\":\"1\",\"mode\":\"exposure
10:37:12.640 18804 18981 I K-radio-Request: │ │ \",\"pageId\":\"\",\"action_id\":\"4232\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"30012
10:37:12.640 18804 18981 I K-radio-Request: │ │ 0013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":
10:37:12.640 18804 18981 I K-radio-Request: │ │ \"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"even
10:37:12.640 18804 18981 I K-radio-Request: │ │ tcode\":\"330003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DF
10:37:12.640 18804 18981 I K-radio-Request: │ │ TC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998
10:37:12.640 18804 18981 I K-radio-Request: │ │ \",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853
10:37:12.648 18804 18804 I ActivityLifecycle: -------------------SettingActivity onStop Start (onActivityPreStopped) -------------------
10:37:12.648 18804 18981 I K-radio-Request: │ │ a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":
10:37:12.648 18804 18981 I K-radio-Request: │ │ \"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"times
10:37:12.648 18804 18981 I K-radio-Request: │ tamp\":\"1756089418431\",\"
10:37:12.648 18804 18981 I K-radio-Request: │ │ udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.648 18804 18981 I K-radio-Request: │ │     "{\"buttonid\":\"08 disagree\",\"buttonname\":\"取消并退出\",\"controltype\":\"1\",\"dialogid\":\"1\",\"mode\":
10:37:12.648 18804 18981 I K-radio-Request: │ │ \"exposure\",\"pageId\":\"\",\"action_id\":\"4233\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2
10:37:12.648 18804 18981 I K-radio-Request: │ │ \":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"
10:37:12.648 18804 18981 I K-radio-Request: │ │ carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv
10:37:12.648 18804 18981 I K-radio-Request: │ │ 2\",\"eventcode\":\"330003\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufactu
10:37:12.648 18804 18981 I K-radio-Request: │ │ rer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa3398202506
10:37:12.648 18804 18981 I K-radio-Request: │ │ 1710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fb
10:37:12.648 18804 18981 I K-radio-Request: │ │ d26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen
10:37:12.649 18804 18981 I K-radio-Request: │ │ _height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"17560894
10:37:12.649 18804 18981 I K-radio-Request: │ │ 18437\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.649 18804 18981 I K-radio-Request: │ │     "{\"pageid\":\"\",\"pagetime\":\"13885\",\"action_id\":\"4234\",\"app_mode\":\"0\",\"app_version\":\"3.01\
10:37:12.649 18804 18981 I K-radio-Request: │ │ ",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car
10:37:12.649 18804 18981 I K-radio-Request: │ │ _brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsourc
10:37:12.649 18804 18981 I K-radio-Request: │ │ e\":\"vehicleeqv2\",\"eventcode\":\"210004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.6343
10:37:12.649 18804 18981 I K-radio-Request: │ │ 34\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\
10:37:12.649 18804 18981 I K-radio-Request: │ │ ":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"
10:37:12.649 18804 18981 I K-radio-Request: │ │ playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\
10:37:12.649 18804 18981 I K-radio-Request: │ │ ":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timest
10:37:12.649 18804 18981 I K-radio-Request: │ │ amp\":\"1756089432347\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}",
10:37:12.649 18804 18981 I K-radio-Request: │ │     "{\"dialogid\":\"1\",\"duration\":13885,\"pageId\":\"122114\",\"action_id\":\"4235\",\"app_mode\":\"0\",\"
10:37:12.649 18804 18981 I K-radio-Request: │ │ app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationc
10:37:12.649 18804 18981 I K-radio-Request: │ │ ode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\"
10:37:12.649 18804 18981 I K-radio-Request: │ │ :\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"330004\",\"ip\":\"*************\",\"lat\":\"31.290464
10:37:12.649 18804 18981 I K-radio-Request: │ │ \",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\
10:37:12.649 18804 18981 I K-radio-Request: │ │ "oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\
10:37:12.649 18804 18981 I K-radio-Request: │ │ "page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\
10:37:12.649 18804 18981 I K-radio-Request: │ │ ",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"source
10:37:12.649 18804 18981 I K-radio-Request: │ │ type\":\"3\",\"timestamp\":\"1756089432355\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}"
10:37:12.649 18804 18981 I K-radio-Request: │ │ ]
10:37:12.649 18804 18981 I K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:12.650 18804 18804 I ActivityLifecycle: -------------------SettingActivity onStop Finished (onActivityStopped) -------------------
10:37:12.650 18804 18804 D ActivityLifecycle: activity set size 1
10:37:12.650 18804 18804 V FragmentManager: computeExpectedState() of 4 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.650 18804 18804 D FragmentManager: movefrom STARTED: t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.650 18804 18804 D FragmentLifecycle: -------------------onFragmentStopped: t---------------------------
10:37:12.650 18804 18804 V FragmentManager: computeExpectedState() of 4 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.650 18804 18804 V FragmentManager: computeExpectedState() of 4 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.650 18804 18804 D FragmentManager: movefrom STARTED: j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.651 18804 18804 D FragmentLifecycle: -------------------onFragmentStopped: j---------------------------
10:37:12.651 18804 18804 V FragmentManager: computeExpectedState() of 4 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.651 18804 18804 V FragmentManager: computeExpectedState() of 4 for j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.651 18804 18804 V FragmentManager: computeExpectedState() of 4 for t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.651 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.652 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:37:12.655 18804 18960 I broadcastDataList:: 0
10:37:12.658 18804 18804 I report_tag: │ 添加数据上报事件.是否立即:false事件id = 210004 json = {"pageid":"122114","pagetime":"15780","action_id":"4236","app_mode":"0","app_version":"3.01","app_version2":"300120013","appid":"wa3398","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"0","channel":"com.edog.car.mengshi_kradio","developer":"5680411","dsource":"vehicleeqv2","eventcode":"210004","ip":"*************","lat":"31.290464","lon":"121.634334","manufacturer":"DFTC","market_type":"","model":"M18","network":"1","oem":"","openid":"wa33982025061710012998","operator":"0","os":"android","osversion":"11","page":"122114","playid":"841fbd26a899853a6f8f4a597e24860b","product_id":"1","report_timely":"0","screen_direction":"0","screen_height":"962","screen_width":"1920","sessionid":"16","sourcetype":"3","timestamp":"1756089432651","udid":"devi1a9718b377b801ad","wifi":"1"}
10:37:12.658 18804 18804 I BaseDialogFragment: report=15780
10:37:12.659 18804 18804 D BaseActivity: Theme observer unregistered in SettingActivity
10:37:12.659 18804 18804 I ActivityLifecycle: -------------------SettingActivity onSaveInstanceState Start (onActivityPreSaveInstanceState) -------------------
10:37:12.659 18804 18804 I ActivityLifecycle: -------------------SettingActivity onSaveInstanceState Finished (onActivitySaveInstanceState) -------------------
10:37:12.659 18804 18804 V FragmentManager: saveAllState: no fragments!
10:37:12.660 18804 18804 V FragmentManager: Saved state of j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting): Bundle[{android:style=1, android:theme=2131820777, fragmentation_arg_container=0, fragmentation_invisible_when_leave=false, fragmentation_state_save_animator=me.yokeyword.fragmentation.anim.FragmentAnimator@65124c3, fragmentation_compat_replace=false, androidx.lifecycle.BundlableSavedStateRegistry.key=Bundle[{}], android:view_registry_state=Bundle[{androidx.lifecycle.BundlableSavedStateRegistry.key=Bundle[{}]}], fragmentation_state_save_status=false, android:savedDialogState=Bundle[{android:dialogHierarchy=Bundle[{android:views={16908290=android.view.AbsSavedState$1@6f3133f}}], android:dialogShowing=false}], android:view_state={2131296551=android.view.AbsSavedState$1@6f3133f, 2131296570=CompoundButton.SavedState{b924140 checked=false}, 2131296571=android.view.AbsSavedState$1@6f3133f, 2131296723=android.view.AbsSavedState$1@6f3133f, 2131296724=android.view.AbsSavedState$1@6f3133f, 2131297528=android.view.AbsSavedState$1@6f3133f, 2131297880=android.view.AbsSavedState$1@6f3133f, 2131297917=android.view.AbsSavedState$1@6f3133f, 2131297918=android.view.AbsSavedState$1@6f3133f, 2131297919=TextView.SavedState{4a77879 start=17 end=25}, 2131297920=android.view.AbsSavedState$1@6f3133f}}]
10:37:12.660 18804 18804 V FragmentManager: saveAllState: no fragments!
10:37:12.660 18804 18804 V FragmentManager: Saved state of t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}: Bundle[{androidx.lifecycle.BundlableSavedStateRegistry.key=Bundle[{}]}]
10:37:12.660 18804 18804 V FragmentManager: saveAllState: adding fragment (7c6a9e69-945c-494f-96ad-82ab47901e36): t{97e4d66} (7c6a9e69-945c-494f-96ad-82ab47901e36 tag=com.bumptech.glide.manager){parent=null}
10:37:12.660 18804 18804 V FragmentManager: saveAllState: adding fragment (f6b1995d-3878-4a82-ab94-985e79f34785): j{c01629b} (f6b1995d-3878-4a82-ab94-985e79f34785 tag=permission_setting)
10:37:12.663 18804 18804 I player_log_tag: .onProgress->progress=339043;duration=477962
10:37:12.663 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=21120
10:37:12.663 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=339043
10:37:12.663 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 959, 已经累加的时间： 21120
10:37:12.665 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "COMMIT;" took 13.000 ms
10:37:12.666 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:37:12.720 18804 18981 I K-radio-Response: │ ┌────── Response ───────────────────────────────────────────────────────────────────────
10:37:12.720 18804 18981 I K-radio-Response: │ │ URL: https://iovmsg.radio.cn/k
10:37:12.720 18804 18981 I K-radio-Response: │ │ /k - is success : true - Received in: 70ms
10:37:12.720 18804 18981 I K-radio-Response: │ │ 
10:37:12.720 18804 18981 I K-radio-Response: │ │ Status Code: 204 / 
10:37:12.720 18804 18981 I K-radio-Response: │ │ 
10:37:12.720 18804 18981 I K-radio-Response: │ │ Headers:
10:37:12.720 18804 18981 I K-radio-Response: │ │ ┌ date: Mon, 25 Aug 2025 02:37:12 GMT
10:37:12.720 18804 18981 I K-radio-Response: │ │ └ access-control-allow-origin: *
10:37:12.720 18804 18981 I K-radio-Response: │ │ 
10:37:12.720 18804 18981 I K-radio-Response: │ 
10:37:12.720 18804 18981 I K-radio-Response: │ │ Omitted response body
10:37:12.720 18804 18981 I K-radio-Response: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:12.720 18804 18981 I report_tag: │ 发送完数据: isSuccessful = true message =  code = 204 body = null
10:37:12.721 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.721 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:37:12.726 18804 18804 I chromium: [INFO:CONSOLE(45)] "true", source: https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0 (45)
10:37:12.733 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 12.000 ms
10:37:12.734 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:12.736 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.736 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "DELETE FROM "REPORT_DATA" WHERE "REPORT_DATA"."_id"=?" took 0.000 ms
10:37:12.737 18804 18848 I chatty  : uid=10084(com.edog.car) RxCachedThreadS identical 8 lines
10:37:12.737 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "DELETE FROM "REPORT_DATA" WHERE "REPORT_DATA"."_id"=?" took 0.000 ms
10:37:12.745 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 8.000 ms
10:37:12.745 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:12.746 18804 18843 D greenDAO: Built SQL for query: SELECT T."_id",T."TYPE",T."SEND_STR" FROM "REPORT_DATA" T  LIMIT ?
10:37:12.746 18804 18843 D greenDAO: Values for query: [10]
10:37:12.747 18804 18843 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:12.747 18804 18843 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT T."_id",T."TYPE",T."SEND_STR" FROM "REPORT_DATA" T  LIMIT ?" took 0.000 ms
10:37:12.747 18804 18843 D SQLiteCursor: received count(*) from native_fill_window: 1
10:37:12.748 18804 18804 I ReportNetworkHelper: │ request,json=["{\"pageid\":\"122114\",\"pagetime\":\"15780\",\"action_id\":\"4236\",\"app_mode\":\"0\",\"app_version\":\"3.01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dsource\":\"vehicleeqv2\",\"eventcode\":\"210004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.634334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"openid\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\",\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_direction\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"timestamp\":\"1756089432651\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}"]
10:37:12.749 18804 18886 I RequestInterceptManager: 替换完成 url= https://iovmsg.radio.cn/k timestamp:1756089432749
10:37:12.751 18804 18886 I K-radio-Request: │ ┌────── Request ────────────────────────────────────────────────────────────────────────
10:37:12.752 18804 18886 I K-radio-Request: │ │ URL: https://iovmsg.radio.cn/k
10:37:12.752 18804 18886 I K-radio-Request: │ │ 
10:37:12.752 18804 18886 I K-radio-Request: │ │ Method: @POST
10:37:12.752 18804 18886 I K-radio-Request: │ │ 
10:37:12.752 18804 18886 I K-radio-Request: │ │ Headers:
10:37:12.752 18804 18886 I K-radio-Request: │ │ ┌ Content-Type: application/json; charset=utf-8
10:37:12.752 18804 18886 I K-radio-Request: │ │ ├ Content-Length: 958
10:37:12.752 18804 18886 I K-radio-Request: │ │ ├ Host: iovmsg.radio.cn
10:37:12.752 18804 18886 I K-radio-Request: │ │ ├ Connection: Keep-Alive
10:37:12.752 18804 18886 I K-radio-Request: │ │ ├ Accept-Encoding: gzip
10:37:12.752 18804 18886 I K-radio-Request: │ │ └ User-Agent: okhttp/3.14.9
10:37:12.752 18804 18886 I K-radio-Request: │ │ Body:
10:37:12.752 18804 18886 I K-radio-Request: │ │ ["{\"pageid\":\"122114\",\"pagetime\":\"15780\",\"action_id\":\"4236\",\"app_mode\":\"0\",\"app_version\":\"3.
10:37:12.752 18804 18886 I K-radio-Request: │ │ 01\",\"app_version2\":\"300120013\",\"appid\":\"wa3398\",\"appid_type\":\"1\",\"applicationcode\":\"17000\",\"
10:37:12.752 18804 18886 I K-radio-Request: │ │ car_brand\":\"\",\"carrier\":\"0\",\"channel\":\"com.edog.car.mengshi_kradio\",\"developer\":\"5680411\",\"dso
10:37:12.752 18804 18886 I K-radio-Request: │ │ urce\":\"vehicleeqv2\",\"eventcode\":\"210004\",\"ip\":\"*************\",\"lat\":\"31.290464\",\"lon\":\"121.6
10:37:12.752 18804 18886 I K-radio-Request: │ │ 34334\",\"manufacturer\":\"DFTC\",\"market_type\":\"\",\"model\":\"M18\",\"network\":\"1\",\"oem\":\"\",\"open
10:37:12.752 18804 18886 I K-radio-Request: │ │ id\":\"wa33982025061710012998\",\"operator\":\"0\",\"os\":\"android\",\"osversion\":\"11\",\"page\":\"122114\"
10:37:12.752 18804 18886 I K-radio-Request: │ │ ,\"playid\":\"841fbd26a899853a6f8f4a597e24860b\",\"product_id\":\"1\",\"report_timely\":\"0\",\"screen_directi
10:37:12.752 18804 18886 I K-radio-Request: │ │ on\":\"0\",\"screen_height\":\"962\",\"screen_width\":\"1920\",\"sessionid\":\"16\",\"sourcetype\":\"3\",\"tim
10:37:12.752 18804 18886 I K-radio-Request: │ │ estamp\":\"1756089432651\",\"udid\":\"devi1a9718b377b801ad\",\"wifi\":\"1\"}"]
10:37:12.752 18804 18886 I K-radio-Request: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:12.756 18804 18804 D HistoryFragment: 主题切换UI刷新完成
10:37:12.770 18804 18804 I chromium: [INFO:CONSOLE(1)] "灏嗘牴鎹凡鏈夌殑meta鏍囩鏉ヨ缃缉鏀炬瘮渚�", source: https://m.kaolafm.com/location/js/flexible.js (1)
10:37:12.779 18804 18804 I chromium: [INFO:CONSOLE(389)] "isSupportDark false", source: https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0 (389)
10:37:12.779 18804 18804 I chromium: [INFO:CONSOLE(408)] "marginTopmarginTop false", source: https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0 (408)
10:37:12.789 18804 18886 I K-radio-Response: │ ┌────── Response ───────────────────────────────────────────────────────────────────────
10:37:12.789 18804 18886 I K-radio-Response: │ │ URL: https://iovmsg.radio.cn/k
10:37:12.789 18804 18886 I K-radio-Response: │ │ /k - is success : true - Received in: 35ms
10:37:12.789 18804 18886 I K-radio-Response: │ │ 
10:37:12.789 18804 18886 I K-radio-Response: │ │ Status Code: 204 / 
10:37:12.789 18804 18886 I K-radio-Response: │ │ 
10:37:12.789 18804 18886 I K-radio-Response: │ │ Headers:
10:37:12.789 18804 18886 I K-radio-Response: │ │ ┌ date: Mon, 25 Aug 2025 02:37:12 GMT
10:37:12.789 18804 18886 I K-radio-Response: │ │ └ access-control-allow-origin: *
10:37:12.789 18804 18886 I K-radio-Response: │ │ 
10:37:12.789 18804 18886 I K-radio-Response: │ 
10:37:12.789 18804 18886 I K-radio-Response: │ │ Omitted response body
10:37:12.789 18804 18886 I K-radio-Response: │ └───────────────────────────────────────────────────────────────────────────────────────
10:37:12.790 18804 18886 I report_tag: │ 发送完数据: isSuccessful = true message =  code = 204 body = null
10:37:12.791 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:12.792 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "DELETE FROM "REPORT_DATA" WHERE "REPORT_DATA"."_id"=?" took 0.000 ms
10:37:12.805 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 13.000 ms
10:37:12.805 18804 18980 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:13.615 18804 18804 I player_log_tag: .onProgress->progress=340004;duration=477962
10:37:13.615 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=22081
10:37:13.615 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=340004
10:37:13.616 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 961, 已经累加的时间： 22081
10:37:13.656 18804 18960 I broadcastDataList:: 0
10:37:13.706 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851888
10:37:13.906 18804 19144 I SocketClient: EVENT_PACKET----->type = pong---->data = null
10:37:13.913 18804 19145 I SocketClient: EVENT_PACKET----->type = ping---->data = null
10:37:14.656 18804 18960 I broadcastDataList:: 0
10:37:14.698 18804 18804 I player_log_tag: .onProgress->progress=341083;duration=477962
10:37:14.698 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=23160
10:37:14.698 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=341083
10:37:14.698 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1079, 已经累加的时间： 23160
10:37:14.698 18804 18872 I HistoryItem: timeStamp = 1756089434698
10:37:14.699 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:14.700 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 1.000 ms
10:37:14.708 18804 18872 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 8.000 ms
10:37:14.708 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@c6c83ed不需要切换线程，直接运行。
10:37:14.708 18804 18804 D K-radio : │ proceed: result=0
10:37:14.709 18804 18804 D K-radio : │ proceed: result=0
10:37:14.709 18804 18982 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:14.709 18804 18982 D greenDAO: Values for query: [99]
10:37:14.710 18804 18982 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:14.711 18804 18982 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 1.000 ms
10:37:14.711 18804 18982 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:14.713 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:14.714 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:14.714 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:14.714 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:14.714 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:14.714 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:14.714 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:14.714 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:14.714 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:14.714 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:14.714 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:15.245 18804 18997 V IJKMEDIA: read thread: run in read loop - 2888949
10:37:15.656 18804 18960 I broadcastDataList:: 0
10:37:15.657 18804 18804 I player_log_tag: .onProgress->progress=342043;duration=477962
10:37:15.657 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=24120
10:37:15.657 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=342043
10:37:15.657 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 960, 已经累加的时间： 24120
10:37:16.544 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPause Start (onActivityPrePaused) -------------------
10:37:16.544 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPause Finished (onActivityPaused) -------------------
10:37:16.544 18804 18804 D ActivityLifecycle: activity set size 0
10:37:16.558 18804 18804 D BaseActivity: -------------------WebViewActivity onWindowFocusChanged start: hasFocus=false-------------------
10:37:16.656 18804 18960 I broadcastDataList:: 0
10:37:16.657 18804 18804 I player_log_tag: .onProgress->progress=343003;duration=477962
10:37:16.658 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=25080
10:37:16.658 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=343003
10:37:16.658 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 960, 已经累加的时间： 25080
10:37:16.659 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStop Start (onActivityPreStopped) -------------------
10:37:16.662 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStop Finished (onActivityStopped) -------------------
10:37:16.662 18804 18804 D ActivityLifecycle: activity set size 0
10:37:16.664 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:16.665 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "INSERT OR REPLACE INTO "CONFIG_DATA" ("_id","TYPE","JSON") VALUES (?,?,?)" took 0.000 ms
10:37:16.673 18804 18804 I report_tag: │ 添加数据上报事件.是否立即:false事件id = 210004 json = {"pageid":"","pagetime":"4202","action_id":"4237","app_mode":"0","app_version":"3.01","app_version2":"300120013","appid":"wa3398","appid_type":"1","applicationcode":"17000","car_brand":"","carrier":"0","channel":"com.edog.car.mengshi_kradio","developer":"5680411","dsource":"vehicleeqv2","eventcode":"210004","ip":"*************","lat":"31.290464","lon":"121.634334","manufacturer":"DFTC","market_type":"","model":"M18","network":"1","oem":"","openid":"wa33982025061710012998","operator":"0","os":"android","osversion":"11","page":"","playid":"841fbd26a899853a6f8f4a597e24860b","product_id":"1","report_timely":"0","screen_direction":"0","screen_height":"962","screen_width":"1920","sessionid":"16","sourcetype":"3","timestamp":"1756089436663","udid":"devi1a9718b377b801ad","wifi":"1"}
10:37:16.673 18804 18804 I BaseDialogFragment: report=4202
10:37:16.674 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "BEGIN EXCLUSIVE;" took 1.000 ms
10:37:16.674 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "INSERT INTO "REPORT_DATA" ("_id","TYPE","SEND_STR") VALUES (?,?,?)" took 0.000 ms
10:37:16.676 18804 18804 D BaseActivity: Theme observer unregistered in WebViewActivity
10:37:16.677 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onSaveInstanceState Start (onActivityPreSaveInstanceState) -------------------
10:37:16.677 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onSaveInstanceState Finished (onActivitySaveInstanceState) -------------------
10:37:16.677 18804 18804 V FragmentManager: saveAllState: no fragments!
10:37:16.678 18804 18804 I WebViewActivity: 保存URL到savedInstanceState: https://m.kaolafm.com/location/partServerAgreement.html?theme=light&bgColor=transparent&contentSize=29&showTitle=1&marginL=0&unit=1&marginR=44&textIndent=0
10:37:16.678 18804 18804 I WebViewActivity: 保存标题到savedInstanceState: 用户服务协议
10:37:16.678 18804 18804 I WebViewActivity: 保存pageId到savedInstanceState: 131310
10:37:16.684 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "COMMIT;" took 19.000 ms
10:37:16.684 18804 18860 V SQLiteTime: /data/user/0/com.edog.car/databases/reportConfig.db: "SELECT COUNT(*) FROM "CONFIG_DATA"" took 0.000 ms
10:37:16.690 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "COMMIT;" took 16.000 ms
10:37:16.690 18804 18981 V SQLiteTime: /data/user/0/com.edog.car/databases/reportData.db: "SELECT COUNT(*) FROM "REPORT_DATA"" took 0.000 ms
10:37:16.695 18804 18804 I AppComponentCallbacks: onTrimMemory: 内存情况=20
10:37:16.695 18804 18804 D LruBitmapPool: trimMemory, level=20
10:37:16.695 18804 18804 D LruBitmapPool: clearMemory
10:37:16.708 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851885
10:37:17.657 18804 18960 I broadcastDataList:: 0
10:37:17.697 18804 18804 I player_log_tag: .onProgress->progress=344083;duration=477962
10:37:17.697 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=26160
10:37:17.697 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=344083
10:37:17.697 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 1080, 已经累加的时间： 26160
10:37:17.715 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onCreate Start (onActivityPreCreated) -------------------
10:37:17.718 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onCreate Finished (onActivityCreated) -------------------
10:37:17.718 18804 18804 D Activity: ====>> oncreate GCB_MODEL:---0
10:37:17.746 18804 18804 I WebViewActivity: 从Intent获取参数 - url:null, title:null, pageId:null
10:37:17.746 18804 18804 I WebViewActivity: url:null
10:37:17.747 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPostCreate Finished (onActivityPostCreated) -------------------
10:37:17.748 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStart Start (onActivityPreStarted) -------------------
10:37:17.749 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onStart Finished (onActivityStarted) -------------------
10:37:17.749 18804 18804 D ActivityLifecycle: activity set size 1
10:37:17.749 18804 18804 I IntentUtils: notifyAppOnResume------------>
10:37:17.753 18804 18804 D BaseActivity: Theme observer registered in WebViewActivity
10:37:17.753 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:17.753 18804 18804 D BaseActivity: Initial theme from settings: theme.nonight
10:37:17.753 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:17.753 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: forceSave = true, theme = theme.nonight, oldUI skin = day.skin, preTheme = unknown
10:37:17.753 18804 18804 I KRadioConfigChangeImpl: isSameTheme: already is day theme and day skin
10:37:17.753 18804 18804 D kradio.home: 收到主题变化事件: isSameTheme
10:37:17.753 18804 18804 D kradio.home: 收到isSameTheme事件，延迟更新用户头像
10:37:17.753 18804 18804 D HistoryFragment: 收到主题变化事件: isSameTheme
10:37:17.753 18804 18804 D ComprehensiveUserInfoFragment: 收到主题变化事件: isSameTheme
10:37:17.753 18804 18804 D ComprehensiveUserInfoFragment: 收到isSameTheme事件，延迟刷新UI状态
10:37:17.754 18804 18804 W Fragmentation: Warning: Perform this commit() action after onSaveInstanceState!
10:37:17.754 18804 18804 V FragmentManager: Commit: BackStackEntry{e14fbce com.kaolafm.kradio.user.comprehensive.ui.x}
10:37:17.754 18804 18804 D FragmentManager:   mName=com.kaolafm.kradio.user.comprehensive.ui.x mIndex=-1 mCommitted=false
10:37:17.754 18804 18804 D FragmentManager:   mTransition=#1001  Operations:
10:37:17.754 18804 18804 D FragmentManager:     Op #0: REPLACE x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:17.754 18804 18804 I SettingActivity: onThemeEvent() --- theme = isSameTheme
10:37:17.754 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:17.755 18804 18804 D WebViewActivity: 收到主题变化事件: isSameTheme
10:37:17.755 18804 18804 D WebViewActivity: 收到isSameTheme事件，读取当前Settings主题状态
10:37:17.755 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:17.757 18804 18804 D WebViewActivity: 收到主题变化事件: isSameTheme
10:37:17.757 18804 18804 D WebViewActivity: 收到isSameTheme事件，读取当前Settings主题状态
10:37:17.757 18804 18804 D BaseActivity: getCurrentThemeFromSettings: theme.nonight
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: isSameTheme: preTheme = unknown, newTheme = theme.nonight
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: isSameTheme: theme changed from unknown to theme.nonight
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: isSameTheme: last skin = day.skin
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: isSameTheme: already is day theme and day skin
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: old skin = day.skin
10:37:17.758 18804 18804 I KRadioConfigChangeImpl: saveConfiguration: SAVE_THEME = theme.nonight
10:37:17.758 18804 18804 D BaseActivity: -------------------WebViewActivity onPostCreate start -------------------
10:37:17.759 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onResume Start (onActivityPreResumed) -------------------
10:37:17.759 18804 18804 D ActivityLifecycle: activity set size 1
10:37:17.759 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onResume Finished (onActivityResumed) -------------------
10:37:17.759 18804 18804 D BaseActivity: -------------------WebViewActivity onPostResume start -------------------
10:37:17.759 18804 18804 I ActivityLifecycle: -------------------WebViewActivity onPostResume Finished (onActivityPostResumed) -------------------
10:37:17.799 18804 18804 V FragmentManager: Run: BackStackEntry{e14fbce #5 com.kaolafm.kradio.user.comprehensive.ui.x}
10:37:17.799 18804 18804 V FragmentManager: Bump nesting in BackStackEntry{e14fbce #5 com.kaolafm.kradio.user.comprehensive.ui.x} by 1
10:37:17.799 18804 18804 V FragmentManager: computeExpectedState() of 4 for x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:17.799 18804 18804 V FragmentManager: computeExpectedState() of 4 for x{87ac75c} (778e70de-9de0-4d34-bbcb-8170bdce5f39 id=0x7f0906f4 tag=com.kaolafm.kradio.user.comprehensive.ui.x)
10:37:17.824 18804 18804 D BaseActivity: -------------------WebViewActivity onEnterAnimationComplete start -------------------
10:37:17.825 18804 18804 D BaseActivity: -------------------WebViewActivity onEnterAnimationComplete start -------------------
10:37:17.866 18804 18804 D BaseActivity: -------------------WebViewActivity onWindowFocusChanged start: hasFocus=true-------------------
10:37:18.056 18804 18804 D HistoryFragment: 主题切换UI刷新完成
10:37:18.576 18804 18832 I NetworkManager: Socket test thread is running!
10:37:18.576 18804 18832 I NetworkManager: Socket test start
10:37:18.576 18804 18832 I NetworkManager: ip: iovopen.radio.cn
10:37:18.619 18804 18832 I NetworkManager: Socket test success finish
10:37:18.656 18804 18804 I player_log_tag: .onProgress->progress=345042;duration=477962
10:37:18.656 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=27119
10:37:18.656 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=345042
10:37:18.656 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 959, 已经累加的时间： 27119
10:37:18.656 18804 18983 I HistoryItem: timeStamp = 1756089438656
10:37:18.657 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "BEGIN EXCLUSIVE;" took 0.000 ms
10:37:18.658 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "INSERT OR REPLACE INTO "HISTORY_ITEM" ("TYPE","RADIO_ID","TYPE_ID","RADIO_TITLE","PIC_URL","AUDIO_ID","AUDIO_TITLE","PLAY_URL","PLAYED_TIME","DURATION","IS_OFFLINE","TIME_STAMP","ORDER_NUM","OFFLINE_PLAY_URL","SHARE_URL","CATEGORY_ID","PARAM_ONE","PARAM_TWO","SOURCE_URL","ORDER_MODE","IS_PLAYING","FINE","VIP","FREQ","BROADCAST_SORT","LISTEN_COUNT","RADIO_UPDATE_TIME","CURRENT_PROGRAM_NAME") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" took 0.000 ms
10:37:18.668 18804 18983 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "COMMIT;" took 10.000 ms
10:37:18.670 18804 18804 D K-radio : │ intercept: com.kaolafm.kradio.component.c$c@3b0c863不需要切换线程，直接运行。
10:37:18.670 18804 18804 D K-radio : │ proceed: result=0
10:37:18.670 18804 18804 D K-radio : │ proceed: result=0
10:37:18.671 18804 18848 D greenDAO: Built SQL for query: SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?
10:37:18.671 18804 18848 D greenDAO: Values for query: [99]
10:37:18.672 18804 18848 I CursorWindowStats: Created a new Cursor. # Open Cursors=1 (# cursors opened by this proc=1)
10:37:18.672 18804 18848 V SQLiteTime: /data/user/0/com.edog.car/databases/kradio.db: "SELECT T."TYPE",T."RADIO_ID",T."TYPE_ID",T."RADIO_TITLE",T."PIC_URL",T."AUDIO_ID",T."AUDIO_TITLE",T."PLAY_URL",T."PLAYED_TIME",T."DURATION",T."IS_OFFLINE",T."TIME_STAMP",T."ORDER_NUM",T."OFFLINE_PLAY_URL",T."SHARE_URL",T."CATEGORY_ID",T."PARAM_ONE",T."PARAM_TWO",T."SOURCE_URL",T."ORDER_MODE",T."IS_PLAYING",T."FINE",T."VIP",T."FREQ",T."BROADCAST_SORT",T."LISTEN_COUNT",T."RADIO_UPDATE_TIME",T."CURRENT_PROGRAM_NAME" FROM "HISTORY_ITEM" T  ORDER BY T.'TIME_STAMP' DESC LIMIT ?" took 0.000 ms
10:37:18.672 18804 18848 D SQLiteCursor: received count(*) from native_fill_window: 4
10:37:18.675 18804 18804 W System.err: java.lang.NullPointerException: Attempt to invoke virtual method 'boolean java.lang.String.equals(java.lang.Object)' on a null object reference
10:37:18.675 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.j3(SourceFile:3)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.n.G1(SourceFile:19)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.X(SourceFile:22)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent.L(SourceFile:1)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.a(SourceFile:4)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.history.comprehensive.ui.HistoryPresent$b.onSuccess(SourceFile:1)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.p.C(SourceFile:1)
10:37:18.676 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.f.onQuery(Unknown Source:4)
10:37:18.677 18804 18804 W System.err: 	at com.kaolafm.kradio.lib.c.c.l$a.onSuccess(SourceFile:2)
10:37:18.677 18804 18804 W System.err: 	at io.reactivex.d0.c.b.m$a.run(SourceFile:3)
10:37:18.677 18804 18804 W System.err: 	at io.reactivex.android.b.b$b.run(SourceFile:1)
10:37:18.677 18804 18804 W System.err: 	at android.os.Handler.handleCallback(Handler.java:938)
10:37:18.677 18804 18804 W System.err: 	at android.os.Handler.dispatchMessage(Handler.java:99)
10:37:18.677 18804 18804 W System.err: 	at android.os.Looper.loop(Looper.java:223)
10:37:18.677 18804 18804 W System.err: 	at android.app.ActivityThread.main(ActivityThread.java:7672)
10:37:18.677 18804 18804 W System.err: 	at java.lang.reflect.Method.invoke(Native Method)
10:37:18.677 18804 18804 W System.err: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
10:37:18.677 18804 18804 W System.err: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:995)
10:37:18.698 18804 18960 I broadcastDataList:: 0
10:37:19.633 18804 18804 I player_log_tag: .onProgress->progress=346003;duration=477962
10:37:19.633 18804 18804 I player_log_tag: .onProgress->mAudioPlayedTime=28080
10:37:19.633 18804 18804 I player_log_tag: .onProgress->mCurrentPosition=346003
10:37:19.633 18804 18804 I UploadHistory: id = 1005830500095单次需要累加的时间： 961, 已经累加的时间： 28080
10:37:19.699 18804 18960 I broadcastDataList:: 0
10:37:20.671 18804 18855 I com.edog.car: Note: end time exceeds INT32_MAX: 9223372036851881